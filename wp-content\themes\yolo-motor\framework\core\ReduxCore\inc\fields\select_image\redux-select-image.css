.redux-container-select_image { margin-top: 2px; margin-left: 5px; width: 100%; margin-bottom: 0; }

.redux-preview-image { max-height: 250px; max-width: 250px; padding: 5px; margin-top: 10px; border: 1px solid #e3e3e3; background: #f7f7f7; border-radius: 3px; }

/*# sourceMappingURL=data:application/json;charset=utf8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoicmVkdXgtc2VsZWN0LWltYWdlLmNzcyIsInNvdXJjZXMiOlsicmVkdXgtc2VsZWN0LWltYWdlLnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUEsQUFBQSw2QkFBNkIsQ0FBQyxFQUMxQixVQUFVLEVBQUUsR0FBRyxFQUNmLFdBQVcsRUFBRSxHQUFHLEVBQ2hCLEtBQUssRUFBRSxJQUFJLEVBQ1gsYUFBYSxFQUFFLENBQUMsR0FDbkI7O0FBRUQsQUFBQSxvQkFBb0IsQ0FBQyxFQUNqQixVQUFVLEVBQUUsS0FBSyxFQUNqQixTQUFTLEVBQUUsS0FBSyxFQUNoQixPQUFPLEVBQUUsR0FBRyxFQUNaLFVBQVUsRUFBRSxJQUFJLEVBQ2hCLE1BQU0sRUFBRSxpQkFBaUIsRUFDekIsVUFBVSxFQUFFLE9BQU8sRUFDbkIsa0JBQWtCLEVBQUUsR0FBRyxFQUN2QixvQkFBb0IsRUFBRSxHQUFHLEVBQ3pCLHFCQUFxQixFQUFFLEdBQUcsRUFDMUIsYUFBYSxFQUFFLEdBQUcsR0FDckIifQ== */

/*# sourceMappingURL=redux-select-image.css.map */
