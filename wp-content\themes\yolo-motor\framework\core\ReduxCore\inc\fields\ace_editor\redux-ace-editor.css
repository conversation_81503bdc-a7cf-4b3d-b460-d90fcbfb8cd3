.redux-container-ace_editor .ace-wrapper { position: static; }

.redux-container-ace_editor .ace_editor { height: 200px; border-radius: 3px; }

.redux-container-ace_editor .ace_gutter { z-index: 1 !important; }

/*# sourceMappingURL=data:application/json;charset=utf8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoicmVkdXgtYWNlLWVkaXRvci5jc3MiLCJzb3VyY2VzIjpbInJlZHV4LWFjZS1lZGl0b3Iuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQSxBQUNJLDJCQUR1QixDQUN2QixZQUFZLENBQUMsRUFDVCxRQUFRLEVBQUUsTUFBTSxHQUNuQjs7QUFITCxBQUtJLDJCQUx1QixDQUt2QixXQUFXLENBQUMsRUFDUixNQUFNLEVBQUUsS0FBSyxFQUNiLHFCQUFxQixFQUFFLEdBQUcsRUFDMUIsa0JBQWtCLEVBQUUsR0FBRyxFQUN2QixhQUFhLEVBQUUsR0FBRyxHQUNyQjs7QUFWTCxBQVlJLDJCQVp1QixDQVl2QixXQUFXLENBQUMsRUFDUixPQUFPLEVBQUUsWUFBWSxHQUN4QiJ9 */

/*# sourceMappingURL=redux-ace-editor.css.map */
