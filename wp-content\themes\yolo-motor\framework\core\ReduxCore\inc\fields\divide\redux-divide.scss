.redux-main {
    .divide {
        height: 20px;
        line-height: 20px;
        float: none;
        border-color: #e7e7e7;
        display: block;
        width: 100%;
        height: 35px !important;
        line-height: 35px !important;
        position: relative;
        margin: 15px 0 10px 0;

        .inner {
            width: 42% !important;
            left: 40% !important;
            margin-left: -6%;
            background-color: #fcfcfc;
            border-color: #e7e7e7;
            position: absolute;
            height: 1px;
            top: 50%;
            width: 100%;
            margin-top: -1px;
            border-top-width: 1px;
            border-top-style: solid;
            span {
                background-color: #fcfcfc;
                border-color: #e7e7e7;
                height: 5px;
                width: 5px;
                border-width: 2px;
                border-style: solid;
                display: block;
                position: absolute;
                left: 50%;
                margin-left: -5px;
                margin-top: -5px;
            }
        }
    }
}

.wp-customizer .redux-container-divide {
    .divide .inner {
        width: 82% !important;
        left: 18% !important;
        margin-left: -8%;
    }
}

