.redux-dimensions-container {
    select,
    .select_wrapper {
        width: 80px !important;
        float: left;
    }

    .field-dimensions-input {
        margin-right: 10px;
        margin-bottom: 7px;
    }
}

@media screen and (max-width: 782px) {
    .redux-dimensions-container {
        .field-dimensions-input {
            input {
                display: inline-block !important;
                width: 100px !important;
            }

            .add-on {
                padding: 7px 4px;
                font-size: 16px;
                line-height: 1.5;
            }
        }

        .select_wrapper {
            margin-top: 6px;
        }
    }
}

