//removeIf(core)
.redux-main {
    .redux-media-slider {
        width: 40%;
        display: inline-block;
        margin-left: 30px;
    }

    .redux-media-filter-container {
        padding-top: 20px;

        .container-label {
            margin-bottom: 20px;
            padding-bottom: 1px;
            border-bottom: 1px solid #e7e7e7;
            font-weight: 600;
            font-size: 12px;
            color: #999;
        }

        .media-filter {
            display: inline-block;
            width: 47%;
            margin-bottom: 5px;

            label {
                display: inline-block;
                width: 130px;
                color: #999;

                &.disabled {

                    .filter-value {
                        color: #ccc;
                    }
                }
            }
        }
    }
}
//removeIf(core)
