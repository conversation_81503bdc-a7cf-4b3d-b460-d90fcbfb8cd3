.redux-container-multi_text {
    ul.redux-multi-text {
        margin: 0;
        padding: 0;
    }

    .redux-multi-text-add {
        clear: both;
        margin: 5px 0;
    }

    a.redux-multi-text-remove {
        &.deletion {
            color: #f00;
            padding: 2px 4px;
            margin-left: 5px;

            &:hover {
                background: #ff0;
                color: #fff;
                text-decoration: none;
            }
        }
    }
}

@media screen and (max-width: 782px) {
    .redux-container-multi_text {
        input {
            clear: both;
        }

        .redux-multi-text-remove {
            margin: 0;
            float: right;
        }
    }
}

.wp-customizer {
    .redux-container-multi_text {
        .button {
            float: right;
        }
        .redux-multi-text-remove {
            float: right;
            margin-bottom: 5px;
        }
        ul.redux-multi-text input {
            width: 100% !important;
        }
    }
}
