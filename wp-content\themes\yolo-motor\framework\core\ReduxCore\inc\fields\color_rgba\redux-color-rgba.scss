.sp-container {
    color: #555;
    border-color: #ccc;
    background: #f7f7f7;
    -webkit-box-shadow: inset 0 1px 0 #fff, 0 1px 0 rgba(0, 0, 0, 0.08);
    box-shadow: inset 0 1px 0 #fff, 0 1px 0 rgba(0, 0, 0, 0.08);
    vertical-align: top;
}

.sp-replacer {
    color: #555;
    border-color: #ccc;
    background: #f7f7f7;
    -webkit-box-shadow: inset 0 1px 0 #fff, 0 1px 0 rgba(0, 0, 0, 0.08);
    box-shadow: inset 0 1px 0 #fff, 0 1px 0 rgba(0, 0, 0, 0.08);
    vertical-align: top;
}

.sp-replacer:focus,
.sp-replacer:hover,
.sp-replacer.focus,
.sp-replacer.hover {
    background: #fafafa;
    border-color: #999;
    color: #222;
}

.sp-replacer:focus,
.sp-replacer.focus {
    -webkit-box-shadow:
        0 0 0 1px #5b9dd9,
        0 0 2px 1px rgba(30, 140, 190, 0.8);
    box-shadow:
        0 0 0 1px #5b9dd9,
        0 0 2px 1px rgba(30, 140, 190, 0.8);

}

.sp-replacer.active:focus {
    -webkit-box-shadow:
        inset 0 2px 5px -3px rgba(0, 0, 0, 0.5),
        0 0 0 1px #5b9dd9,
        0 0 2px 1px rgba(30, 140, 190, 0.8);
    box-shadow:
        inset 0 2px 5px -3px rgba(0, 0, 0, 0.5),
        0 0 0 1px #5b9dd9,
        0 0 2px 1px rgba(30, 140, 190, 0.8);
}

.sp-replacer.active,
.sp-replacer.active:hover,
.sp-replacer:active {
    background: #eee;
    border-color: #999;
    color: #333;
    -webkit-box-shadow: inset 0 2px 5px -3px rgba(0, 0, 0, 0.5);
    box-shadow: inset 0 2px 5px -3px rgba(0, 0, 0, 0.5);
}
