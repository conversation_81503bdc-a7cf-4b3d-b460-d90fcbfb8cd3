<?php
/**
 * "Order received" message.
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/checkout/order-received.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see https://woocommerce.com/document/template-structure/
 * @package WooCommerce\Templates
 * @version 8.8.0
 *
 * @var WC_Order|false $order
 */

defined( 'ABSPATH' ) || exit;
?>
<div class="thankyou-message" style="padding: 80px;">
    <h2>
        <?php 
        	$heading = apply_filters(
                'woocommerce_thankyou_order_received_text',
                esc_html( __( 'Thank you for your order request!', 'woocommerce' ) ),
                $order
            );
            echo $heading;
            ?>
    </h2>
    <p class="woocommerce-notice woocommerce-notice--success woocommerce-thankyou-order-received">
        <?php
        /**
         * Filter the message shown after a checkout is complete.
         *
         * @since 2.2.0
         *
         * @param string         $message The message.
         * @param WC_Order|false $order   The order created during checkout, or false if order data is not available.
         */
/*         $message = apply_filters(
            'woocommerce_thankyou_order_received_text',
            esc_html( __( 'Your order request is being reviewed by someone from our Customer Service team.  Once your order is entered, you will receive an order confirmation from us!')),
            esc_html( __( 'Purchase order is:  #' . get_post_meta($order->get_id(), '_purchase_order_number', true) . '.', 'woocommerce' ) ),
            $order
        );
 */    
        // phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped
        // echo $message;
        ?>
        Your order request is being reviewed by someone from our Customer Service team.  Once your order is entered, you will receive an order confirmation from us!
    </p>
    <p>Purchase order: <?php echo get_post_meta($order->get_id(), '_purchase_order_number', true); ?></p>

    <?php
    // Always show request JSON for debugging if available
    if ($order) {
        $sap_request = get_post_meta($order->get_id(), '_sap_order_request', true);
        if (!empty($sap_request)) {
           // echo '<details style="margin: 20px 0; border: 1px solid #ddd; border-radius: 5px; padding: 10px;">';
            //echo '<summary style="cursor: pointer; font-weight: bold; color: #0073aa;">🔍 Debug: Request Sent to SAP (Click to expand)</summary>';
            //echo '<pre style="background: #f8f9fa; padding: 10px; border-radius: 3px; font-size: 12px; overflow-x: auto; margin-top: 10px; border-left: 3px solid #0073aa;">';
           // echo esc_html($sap_request);
           // echo '</pre>';
            //echo '</details>';
        } else {
            // Debug: Show why request JSON is not available
            if (current_user_can('manage_options')) {
                //echo '<div style="margin: 20px 0; padding: 10px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px;">';
               // echo '<p style="color: #856404; margin: 0;"><strong>Debug (Admin Only):</strong> No request JSON found for order ' . $order->get_id() . '</p>';

                // Show all SAP-related meta
                $all_meta = get_post_meta($order->get_id());
                $sap_meta = array();
                foreach ($all_meta as $key => $value) {
                    if (strpos($key, 'sap') !== false || strpos($key, '_sap') !== false) {
                        $sap_meta[$key] = is_array($value) ? $value[0] : $value;
                    }
                }

                if (!empty($sap_meta)) {
                    echo '<details style="margin-top: 10px;">';
                    echo '<summary style="cursor: pointer; color: #856404;">SAP Meta Fields Found</summary>';
                    echo '<pre style="background: #f8f9fa; padding: 10px; border-radius: 3px; font-size: 11px; margin-top: 5px;">';
                    echo esc_html(json_encode($sap_meta, JSON_PRETTY_PRINT));
                    echo '</pre>';
                    echo '</details>';
                } else {
                    echo '<p style="color: #856404; margin: 5px 0;">No SAP-related meta fields found.</p>';

                    // Show detailed debugging for missing SAP data
                    echo '<details style="margin-top: 10px;">';
                    echo '<summary style="cursor: pointer; color: #d63384;">🔍 Debug: Why SAP Order Creation Failed</summary>';
                    echo '<div style="background: #f8f9fa; padding: 15px; border-radius: 3px; margin-top: 5px;">';

                    // Check required fields
                    $required_fields = [
                        '_purchase_order_number' => 'Purchase Order Number',
                        '_customer_requested_ship_date' => 'Customer Requested Ship Date',
                        '_shipping_method_preference' => 'Shipping Method Preference',
                        '_shipping_account_number' => 'Shipping Account Number',
                        '_special_requests' => 'Special Requests',
                        '_wcmca_shipping_selected_address_id' => 'Selected Shipping Address ID'
                    ];

                    $missing_fields = [];
                    $found_fields = [];

                    foreach ($required_fields as $field => $label) {
                        $value = get_post_meta($order->get_id(), $field, true);
                        if (empty($value)) {
                            $missing_fields[] = $label . " ({$field})";
                        } else {
                            $found_fields[$label] = $value;
                        }
                    }

                    if (!empty($found_fields)) {
                        echo '<h4 style="color: #28a745; margin: 0 0 10px 0;">✅ Fields Found:</h4>';
                        echo '<ul style="margin: 0 0 15px 0;">';
                        foreach ($found_fields as $label => $value) {
                            echo '<li><strong>' . esc_html($label) . ':</strong> ' . esc_html($value) . '</li>';
                        }
                        echo '</ul>';
                    }

                    if (!empty($missing_fields)) {
                        echo '<h4 style="color: #dc3545; margin: 0 0 10px 0;">❌ Missing Required Fields:</h4>';
                        echo '<ul style="margin: 0 0 15px 0;">';
                        foreach ($missing_fields as $field) {
                            echo '<li style="color: #dc3545;">' . esc_html($field) . '</li>';
                        }
                        echo '</ul>';
                    }

                    // Check user and customer data
                    $user_id = $order->get_user_id();
                    if ($user_id) {
                        $main_user_id = function_exists('get_main_b2b_admin_id') ? get_main_b2b_admin_id($user_id) : $user_id;
                        $customer_data = function_exists('hytec_get_sap_customer_for_user') ? hytec_get_sap_customer_for_user($main_user_id) : null;

                        echo '<h4 style="color: #0073aa; margin: 0 0 10px 0;">👤 User & Customer Data:</h4>';
                        echo '<ul style="margin: 0 0 15px 0;">';
                        echo '<li><strong>Order User ID:</strong> ' . $user_id . '</li>';
                        echo '<li><strong>Main User ID:</strong> ' . $main_user_id . '</li>';
                        echo '<li><strong>SAP Customer Data:</strong> ' . ($customer_data ? '✅ Found' : '❌ Not Found') . '</li>';
                        if ($customer_data) {
                            echo '<li><strong>Customer ID:</strong> ' . esc_html($customer_data->customer_id ?? 'N/A') . '</li>';
                            echo '<li><strong>Company Code:</strong> ' . esc_html($customer_data->company_code ?? 'N/A') . '</li>';
                        }
                        echo '</ul>';
                    }

                    // Check if ANY checkout hooks were triggered
                    $test_hook_ran = get_post_meta($order->get_id(), '_test_checkout_hook_ran', true);
                    $test_blocks_hook_ran = get_post_meta($order->get_id(), '_test_blocks_checkout_hook_ran', true);
                    $hook_triggered = get_post_meta($order->get_id(), '_sap_debug_hook_triggered', true);
                    echo '<h4 style="color: #6f42c1; margin: 0 0 10px 0;">🔄 SAP Order Creation Process:</h4>';
                    echo '<ul style="margin: 0 0 15px 0;">';
                    echo '<li><strong>Classic Checkout Hook:</strong> ' . ($test_hook_ran ? '✅ Yes at ' . $test_hook_ran : '❌ No') . '</li>';
                    echo '<li><strong>Blocks Checkout Hook:</strong> ' . ($test_blocks_hook_ran ? '✅ Yes at ' . $test_blocks_hook_ran : '❌ No') . '</li>';
                    echo '<li><strong>SAP Hook Triggered:</strong> ' . ($hook_triggered ? '✅ Yes at ' . $hook_triggered : '❌ No') . '</li>';

                    // Check for any SAP error messages
                    $sap_error = get_post_meta($order->get_id(), '_sap_order_error', true);
                    if ($sap_error) {
                        echo '<li><strong>SAP Error:</strong> <span style="color: #dc3545;">' . esc_html($sap_error) . '</span></li>';
                    }

                    // Check processing status
                    $processing = get_post_meta($order->get_id(), '_sap_order_processing', true);
                    if ($processing) {
                        echo '<li><strong>Status:</strong> <span style="color: #ffc107;">Still Processing...</span></li>';
                    }

                    echo '</ul>';

                    echo '</div>';
                    echo '</details>';
                }

                // Add link to logs
                echo '<p style="margin-top: 10px;"><a href="' . admin_url('admin.php?page=sap-checkout-logs') . '" target="_blank" style="color: #0073aa;">View SAP Checkout Logs</a></p>';
                echo '</div>';
            }
        }
    }
    ?>

    <?php
    // Display SAP order creation response
    if ($order) {
        $sap_response = get_post_meta($order->get_id(), '_sap_order_response', true);
        $sap_response_code = get_post_meta($order->get_id(), '_sap_order_response_code', true);
        $sap_error = get_post_meta($order->get_id(), '_sap_order_error', true);
        $sap_created = get_post_meta($order->get_id(), '_sap_order_created', true);
        $sap_status = get_post_meta($order->get_id(), '_sap_order_status', true);
        $sap_processing = get_post_meta($order->get_id(), '_sap_order_processing', true);
        $sap_request = get_post_meta($order->get_id(), '_sap_order_request', true);

        // Debug info for troubleshooting
        $debug_info = array(
            'sap_response' => !empty($sap_response),
            'sap_response_code' => $sap_response_code,
            'sap_error' => !empty($sap_error),
            'sap_created' => $sap_created,
            'sap_status' => $sap_status,
            'sap_processing' => $sap_processing
        );

        if ($sap_created === 'yes' && !empty($sap_response)) {
            //echo '<div class="sap-response-success" style="margin-top: 20px; padding: 15px; background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px;">';
            //echo '<h4 style="color: #155724; margin-bottom: 10px;">✓ Order Successfully Submitted to SAP</h4>';

            $decoded_response = json_decode($sap_response, true);
            if ($decoded_response && is_array($decoded_response)) {
                //echo '<p style="color: #155724; margin: 5px 0;"><strong>Response Details:</strong></p>';
                //echo '<pre style="background: #f8f9fa; padding: 10px; border-radius: 3px; font-size: 12px; overflow-x: auto;">';
                //echo esc_html(json_encode($decoded_response, JSON_PRETTY_PRINT));
                //echo '</pre>';
            } else {
               // echo '<p style="color: #155724;">Raw Response: ' . esc_html($sap_response) . '</p>';
            }

            // Show request JSON for debugging
            if (!empty($sap_request)) {
                //echo '<details style="margin-top: 15px;">';
                //echo '<summary style="cursor: pointer; color: #155724; font-weight: bold;">📤 Request Sent to SAP (Click to expand)</summary>';
                //echo '<pre style="background: #f8f9fa; padding: 10px; border-radius: 3px; font-size: 12px; overflow-x: auto; margin-top: 10px; border-left: 3px solid #28a745;">';
                //echo esc_html($sap_request);
                //echo '</pre>';
                //echo '</details>';
            }

           // echo '</div>';
        } elseif (!empty($sap_error) || $sap_status === 'failed' || (!empty($sap_response) && $sap_created !== 'yes')) {
           // echo '<div class="sap-response-error" style="margin-top: 20px; padding: 15px; background-color: #f8d7da; border: 1px solid #f5c6cb; border-radius: 5px;">';
            //echo '<h4 style="color: #721c24; margin-bottom: 10px;">⚠ SAP Order Submission Issue</h4>';
            if (!empty($sap_error)) {
                //echo '<p style="color: #721c24;">Error: ' . esc_html($sap_error) . '</p>';
            }
            if (!empty($sap_response)) {
               // echo '<p style="color: #721c24; margin: 5px 0;"><strong>SAP Response:</strong></p>';
               // echo '<pre style="background: #f8f9fa; padding: 10px; border-radius: 3px; font-size: 12px; overflow-x: auto;">';
                //echo esc_html($sap_response);
                //echo '</pre>';
            }

            // Show request JSON for debugging
            if (!empty($sap_request)) {
                //echo '<details style="margin-top: 15px;">';
                //echo '<summary style="cursor: pointer; color: #721c24; font-weight: bold;">📤 Request Sent to SAP (Click to expand)</summary>';
               // echo '<pre style="background: #f8f9fa; padding: 10px; border-radius: 3px; font-size: 12px; overflow-x: auto; margin-top: 10px; border-left: 3px solid #dc3545;">';
                //echo esc_html($sap_request);
               // echo '</pre>';
//echo '</details>';
            }

           // echo '<p style="color: #721c24; font-size: 14px;">Your order has been received by our system. Our customer service team will process it manually.</p>';
           // echo '</div>';
        } elseif ($sap_processing === 'yes') {
           // echo '<div class="sap-response-pending" style="margin-top: 20px; padding: 15px; background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px;">';
            //echo '<h4 style="color: #856404; margin-bottom: 10px;">⏳ Processing Order</h4>';
            //echo '<p style="color: #856404;">Your order is currently being submitted to our SAP system...</p>';
            //echo '</div>';
        } else {
            // No processing flag, but no response either - might be an issue
            //echo '<div class="sap-response-pending" style="margin-top: 20px; padding: 15px; background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px;">';
            //echo '<h4 style="color: #856404; margin-bottom: 10px;">⏳ Processing Order</h4>';
            //echo '<p style="color: #856404;">Your order is being processed and will be submitted to our system shortly.</p>';

            // Show request JSON if available
            if (!empty($sap_request)) {
               // echo '<details style="margin-top: 15px;">';
                //echo '<summary style="cursor: pointer; color: #856404; font-weight: bold;">📤 Request Prepared for SAP (Click to expand)</summary>';
               // echo '<pre style="background: #f8f9fa; padding: 10px; border-radius: 3px; font-size: 12px; overflow-x: auto; margin-top: 10px; border-left: 3px solid #ffc107;">';
               // echo esc_html($sap_request);
               // echo '</pre>';
               // echo '</details>';
            }

            // Show debug info for troubleshooting
            if (current_user_can('manage_options')) {
               // echo '<details style="margin-top: 10px;">';
                //echo '<summary style="cursor: pointer; color: #856404;">Debug Info (Admin Only)</summary>';
               // echo '<pre style="background: #f8f9fa; padding: 10px; border-radius: 3px; font-size: 11px; margin-top: 5px;">';
               // echo esc_html(json_encode($debug_info, JSON_PRETTY_PRINT));
               // echo '</pre>';
               /// echo '</details>';
            }
           // echo '</div>';
        }
    }
    ?>

    <a href="/shop/" class="btn orange-bg white px-5 py-2 mt-3">Back to Shop</a>
</div>
