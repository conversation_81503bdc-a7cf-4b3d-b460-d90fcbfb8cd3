.redux-info-field {
    min-height: 20px;
    padding: 8px 19px;
    margin: 10px 0;
    border: 1px solid;
    border-radius: 4px;
    border: 1px solid;
    position: relative;

    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
        border-bottom: 0 !important;
    }

    h3 {
        color: #777;
    }

    .redux-info-icon {
        display: inline-block;
        margin-right: 15px;

        i {
            font-size: 2em;
        }
    }

    .redux-info-desc {
        display: inline-block;
        vertical-align: top;
    }

    &.redux-normal {
        background-color: #eee;
        border-color: #ccc;
        color: #666;

        i {
            color: #c5c5c5;
        }
    }

    &.redux-warning {
        background-color: #fbeba4;
        border-color: #d7c281;
        color: #958234;

        i {
            color: #dcca81;
        }
    }

    &.redux-success {
        background-color: #c4ee91;
        border-color: #71af5d;
        color: #4d7615;

        i {
            color: #a0ca6c;
        }
    }

    &.redux-critical {
        background-color: #fba1a3;
        border-color: #b84f5b;
        color: #981225;

        i {
            color: #dd767d;
        }
    }

    &.redux-info {
        background-color: #d3e4f4;
        border-color: #a9b6c2;
        color: #5c80a1;

        i {
            color: #afc6da;
        }
    }
}

.redux-notice-field {
    margin: 15px 0 0;
    background-color: #fff;
    border: 0;
    border-left: 4px solid #f3f3f3;
    -webkit-box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.1);
    box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.1);
    padding: 1px 12px;

    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
        border-bottom: 0 !important;
    }

    p {
        margin: 0.5em 0;
        padding: 2px;
    }

    .redux-info-icon {
        display: inline-block;
        margin-right: 15px;

        i {
            font-size: 2em;
        }
    }

    .redux-info-desc {
        display: inline-block;
        vertical-align: top;
    }

    &.redux-info {
        border-left: 4px solid #0099d5;
    }

    &.redux-success {
        border-left: 4px solid #7ad03a;
    }

    &.redux-warning {
        border-left: 4px solid #fbeba4;
    }

    &.redux-critical {
        border-left: 4px solid #dd3d36;
    }
}

.redux-main .redux-field-container.redux-container-info {
    padding: 0;
}

.wp-customizer {
    .hasIcon.redux-notice-field,
    .hasIcon.redux-info-field {
        .redux-info-desc {
            display: block;
            margin-left: 43px;
        }
        .redux-info-icon {
            float: left;
        }
    }
    .redux-main .customize-control.customize-control-redux-info {
        border-bottom: 0;
    }
}

