.redux-container-editor {
    .mceLayout td {
        border-width: 1px;
        margin: 0;
        padding: 1px;
    }

    input,
    textarea {
        margin: inherit;
    }

    textarea {
        border-style: none;
        border: 0;
        border-width: 0;
    }

    .wp-editor-container {
        -webkit-border-radius: 3px;
        -moz-border-radius: 3px;
        border-radius: 3px;

        textarea {
            -webkit-border-radius: 0;
            -moz-border-radius: 0;
            border-radius: 0;
            border-style: inherit;
        }
    }

    .quicktags-toolbar input {
        margin: 2px 1px 4px;
        display: inline-block;
        min-width: 26px;
        padding: 2px 4px;
        font: 12px/18px Arial, Helvetica, sans-serif normal;
        color: #464646;
        border: 1px solid #c3c3c3;
        -webkit-border-radius: 3px;
        border-radius: 3px;
        background: #eee;
        background-image: -webkit-gradient(linear, left bottom, left top, from(#e3e3e3), to(#fff));
        background-image: -webkit-linear-gradient(bottom, #e3e3e3, #fff);
        background-image: -moz-linear-gradient(bottom, #e3e3e3, #fff);
        background-image: -o-linear-gradient(bottom, #e3e3e3, #fff);
        background-image: linear-gradient(to top, #e3e3e3, #fff);
    }
}

