.redux-container-palette label { border: 3px solid transparent; border-color: transparent !important; border-radius: 0; width: 100% !important; display: block; }

.redux-container-palette label.ui-button.ui-widget { width: 95%; background: none; padding: 0; display: -webkit-box; display: -webkit-flex; display: -ms-flexbox; display: flex; }

.redux-container-palette label.ui-button.ui-widget .ui-checkboxradio-icon, .redux-container-palette label.ui-button.ui-widget .ui-checkboxradio-icon-space { display: none; }

.redux-container-palette label.ui-button.ui-widget span { padding: 10px; -webkit-box-flex: 1; -webkit-flex-grow: 1; -ms-flex-positive: 1; flex-grow: 1; font-size: 0; line-height: 10px; color: rgba(0, 0, 0, 0); -webkit-transition: all 200ms ease-in-out; transition: all 200ms ease-in-out; text-shadow: 0; }

.redux-container-palette label.ui-button.ui-widget span:hover { -webkit-box-flex: 3; -webkit-flex-grow: 3; -ms-flex-positive: 3; flex-grow: 3; font-weight: bold; min-width: 60px; font-size: 12px; line-height: 10px; color: #333; text-shadow: 0 0 8px #fff, 0 0 8px #fff; }

.redux-container-palette label.ui-state-active { border: 3px solid #333 !important; }

.wp-customizer .redux-main .redux-container-palette label { margin-bottom: 3px; }

/*# sourceMappingURL=data:application/json;charset=utf8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoicmVkdXgtcGFsZXR0ZS5jc3MiLCJzb3VyY2VzIjpbInJlZHV4LXBhbGV0dGUuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQSxBQUNJLHdCQURvQixDQUNwQixLQUFLLENBQUMsRUFDRixNQUFNLEVBQUUscUJBQXFCLEVBQzdCLFlBQVksRUFBRSxzQkFBc0IsRUFDcEMsYUFBYSxFQUFFLENBQUMsRUFDaEIsS0FBSyxFQUFFLGVBQWUsRUFDdEIsT0FBTyxFQUFFLEtBQUssR0F1Q2pCOztBQTdDTCxBQU9RLHdCQVBnQixDQUNwQixLQUFLLEFBTUEsVUFBVSxBQUFBLFVBQVUsQ0FBQyxFQUNsQixLQUFLLEVBQUUsR0FBRyxFQUNWLFVBQVUsRUFBRSxJQUFJLEVBQ2hCLE9BQU8sRUFBRSxDQUFDLEVBQ1YsT0FBTyxFQUFFLElBQUksR0E2QmhCOztBQXhDVCxBQWFZLHdCQWJZLENBQ3BCLEtBQUssQUFNQSxVQUFVLEFBQUEsVUFBVSxDQU1qQixzQkFBc0IsRUFibEMsd0JBQXdCLENBQ3BCLEtBQUssQUFNQSxVQUFVLEFBQUEsVUFBVSxDQU9qQiw0QkFBNEIsQ0FBQyxFQUN6QixPQUFPLEVBQUMsSUFBSSxHQUNmOztBQWhCYixBQWtCWSx3QkFsQlksQ0FDcEIsS0FBSyxBQU1BLFVBQVUsQUFBQSxVQUFVLENBV2pCLElBQUksQ0FBQyxFQUNELE9BQU8sRUFBRSxJQUFJLEVBQ2IsU0FBUyxFQUFFLENBQUMsRUFDWixTQUFTLEVBQUUsQ0FBQyxFQUNaLFdBQVcsRUFBRSxJQUFJLEVBQ2pCLEtBQUssRUFBRSxnQkFBZ0IsRUFDdkIsa0JBQWtCLEVBQUUscUJBQXFCLEVBQ3pDLGVBQWUsRUFBRSxxQkFBcUIsRUFDdEMsY0FBYyxFQUFFLHFCQUFxQixFQUNyQyxhQUFhLEVBQUUscUJBQXFCLEVBQ3BDLFVBQVUsRUFBRSxxQkFBcUIsRUFDakMsV0FBVyxFQUFFLENBQUMsR0FVakI7O0FBdkNiLEFBOEJnQix3QkE5QlEsQ0FDcEIsS0FBSyxBQU1BLFVBQVUsQUFBQSxVQUFVLENBV2pCLElBQUksQUFZQyxNQUFNLENBQUMsRUFDSixTQUFTLEVBQUUsQ0FBQyxFQUNaLFdBQVcsRUFBRSxJQUFJLEVBQ2pCLFNBQVMsRUFBRSxJQUFJLEVBQ2YsU0FBUyxFQUFFLElBQUksRUFDZixXQUFXLEVBQUUsSUFBSSxFQUNqQixLQUFLLEVBQUUsSUFBSSxFQUNYLFdBQVcsRUFBRSwwQkFBMEIsR0FDMUM7O0FBdENqQixBQTBDUSx3QkExQ2dCLENBQ3BCLEtBQUssQUF5Q0EsZ0JBQWdCLENBQUMsRUFDZCxNQUFNLEVBQUUseUJBQXlCLEdBQ3BDOztBQUlULEFBQ0ksY0FEVSxDQUNWLFdBQVcsQ0FBQyx3QkFBd0IsQ0FBQyxLQUFLLENBQUMsRUFDdkMsYUFBYSxFQUFFLEdBQUcsR0FDckIifQ== */

/*# sourceMappingURL=redux-palette.css.map */
