.redux-main {
    .redux-container-color_palette {
        label {
            position: relative;
            display: inline-block;
            padding: 0;
            margin: 0; 
        }
        
        .colors-wrapper {
            max-height: 300px;
            overflow-y: auto;
            padding: 10px;
            display: flex;
            flex-wrap: wrap;
            
            .color-palette-color {
                color: transparent;
                display: block;
                width: 100%;
                height: 100%;
                overflow: hidden;
                border: 1px solid rgba(0, 0, 0, 0.2); 
            }
            
            &.round {
                label {
                    padding: 3px; 
                }
                
                .color-palette-color {
                    border-radius: 50%; 
                }
                
            }

            &.box-shadow .color-palette-color {
                box-shadow: inset 3px 3px 13px 2px rgba(0, 0, 0, 0.22); 
            }

            input {
                display: none; 
                
                &:checked + label .color-palette-color {
                    border: 0;
                    width: 130%;
                    height: 130%;
                    position: relative;
                    left: -15%;
                    top: -15%;
                    z-index: 99;
                    box-shadow: 1px 1px 6px 1px #333333;
                    border: 1px solid rgba(0, 0, 0, 0.3);                    
                }
            }

            &.with-margin label {
                margin: 3px!important; 
            }
        }
    }
}